:root {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --border-color: #DCDFE6;
  --page-background: #F5F7FA;
  --card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

body {
  background-color: var(--page-background);
  color: var(--text-primary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  margin-bottom: 2rem;
}

.section {
  margin-bottom: 3rem;
}

/* 卡片样式 */
.el-card {
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  transition: transform 0.3s ease;
}

.el-card:hover {
  transform: translateY(-5px);
}

/* 按钮样式 */
.el-button {
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 页面标题样式 */
.page-title {
  font-size: 2rem;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.page-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

/* 列表和网格布局 */
.grid-layout {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.list-layout {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

.el-input__inner,
.el-textarea__inner {
  border-radius: 4px;
  transition: all 0.3s ease;
}

.el-input__inner:focus,
.el-textarea__inner:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 图片和头像样式 */
.el-image {
  border-radius: 8px;
  overflow: hidden;
}

.el-avatar {
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .grid-layout {
    grid-template-columns: 1fr;
  }

  .el-card {
    margin-bottom: 15px;
  }
}
