{"name": "tourlink", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@mdi/font": "^7.4.47", "element-plus": "^2.9.10", "pinia": "^2.1.7", "vue": "^3.3.11", "vue-router": "^4.2.5", "vuetify": "^3.8.0-beta.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "path": "^0.12.7", "sass": "^1.89.0", "vite": "^5.0.10"}}